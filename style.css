* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* Header */
.navbar {
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  color: #fff !important;
  letter-spacing: 0.5px;
}

.nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #fff !important;
}

.nav-link.active {
  color: #fff !important;
  border-bottom: 2px solid #ffc107;
}

.form-control {
  border-radius: 20px 0 0 20px;
  border: none;
  padding: 0.5rem 1rem;
}

.form-control:focus {
  box-shadow: none;
  border-color: #ffc107;
}

.btn-warning {
  border-radius: 0 20px 20px 0;
  padding: 0.5rem 1.2rem;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.navbar .fa-solid {
  transition: transform 0.2s ease;
}

.navbar .fa-solid:hover {
  transform: scale(1.1);
}
.text-hover-primary:hover {
  color: #e0a800 !important;
}

/* Footer Styles */
.footer {
  background-color: #2c55a5 !important;
}

.footer-heading {
  color: #ffc107;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer p {
  color: #b3b3b3;
  font-size: 0.9rem;
}

.footer-links a {
  color: #b3b3b3;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

.footer-links a:hover {
  color: #ffc107;
  padding-left: 5px;
}

.social-links a {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  width: 35px;
  height: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: #ffc107;
  color: #000;
  transform: translateY(-3px);
}

.newsletter-form .form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  padding: 0.75rem 1rem;
}

.newsletter-form .form-control::placeholder {
  color: #b3b3b3;
}

.newsletter-form .form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: none;
}

.newsletter-form .btn-warning {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border: none;
}

.payment-icons i {
  color: #b3b3b3;
  transition: color 0.3s ease;
}

.payment-icons i:hover {
  color: #ffc107;
}

.footer-bottom {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.footer-bottom p {
  color: #888;
  font-size: 0.85rem;
}

.bo-cong-thuong {
  filter: brightness(0.9);
  transition: filter 0.3s ease;
}

.bo-cong-thuong:hover {
  filter: brightness(1);
}

@media (max-width: 768px) {
  .footer {
    text-align: center;
  }

  .social-links {
    justify-content: center;
    margin-bottom: 2rem;
  }

  .footer-links {
    margin-bottom: 2rem;
  }
}

/* Carousel Styles */
.carousel-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.carousel-inner {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.carousel-item {
  height: 500px;
}

.carousel-item img {
  object-fit: cover;
  height: 100%;
  filter: brightness(0.7);
}

.carousel-caption {
  bottom: 20%;
  padding: 2rem;
}

.carousel-caption h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.carousel-caption p {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.carousel-caption .btn-warning {
  border-radius: 25px;
  padding: 0.8rem 2rem;
  font-weight: 600;
  border: none;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.carousel-caption .btn-warning:hover {
  transform: translateY(-2px);
  background-color: #ffc107;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.carousel-indicators {
  bottom: 20px;
}

.carousel-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 5px;
  margin: 0 6px;
}

.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  opacity: 0.7;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
}

@media (max-width: 768px) {
  .carousel-item {
    height: 300px;
  }

  .carousel-caption h2 {
    font-size: 1.8rem;
  }

  .carousel-caption p {
    font-size: 1rem;
  }

  .carousel-caption .btn-warning {
    padding: 0.6rem 1.5rem;
  }
}
